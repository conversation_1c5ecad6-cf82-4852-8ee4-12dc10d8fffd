# Airbyte MySQL to Typesense Demo

## 🐳 Start Services
```bash
docker-compose up -d
```

## 🛢️ MySQL
Wait a few seconds, then load the sample data:

```bash
docker exec -it demo-mysql mysql -u root -ppassword < init.sql
```

## 📁 Create Typesense Collection
```bash
bash create_typesense_collection.sh
```

## 🧠 Access Airbyte
- Open http://localhost:8000
- Create a MySQL source (host: host.docker.internal, db: testdb, user: testuser, pass: testpass)
- Create a Webhook destination pointing to:
  - URL: http://host.docker.internal:8108/collections/users/documents/import?action=upsert
  - Header: X-TYPESENSE-API-KEY = xyz123
- Sync the `users` table
