version: "3"

services:
  mysql:
    image: mysql:8
    container_name: demo-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: testdb
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpass
    ports:
      - "3306:3306"

  typesense:
    image: typesense/typesense:latest
    container_name: demo-typesense
    command: '--data-dir /data --api-key=xyz123 --listen-port 8108'
    ports:
      - "8108:8108"
    volumes:
      - ./typesense-data:/data

  airbyte:
    image: airbyte/airbyte:latest
    container_name: demo-airbyte
    ports:
      - "8000:8000"
    command: ["bash", "-c", "./run-ab-platform.sh"]
    working_dir: /airbyte
    tty: true
    stdin_open: true
